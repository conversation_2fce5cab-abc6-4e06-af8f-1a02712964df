import { NextRequest, NextResponse } from 'next/server'
import { requireAdmin, getCurrentUser } from '@/lib/auth'
import { query } from '@/lib/local-db'

// PATCH /api/admin/ip-deletion-requests/[id] - Process IP deletion request
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const adminUser = await requireAdmin()
    const { id: requestId } = await params
    const body = await request.json()
    const { action, notes } = body
    
    if (!action || !['approve', 'reject'].includes(action)) {
      return NextResponse.json(
        { error: 'Action must be either "approve" or "reject"' },
        { status: 400 }
      )
    }
    
    // Get the deletion request
    const requestResult = await query(`
      SELECT * FROM data_deletion_requests WHERE id = $1
    `, [requestId])
    
    if (requestResult.rows.length === 0) {
      return NextResponse.json(
        { error: 'Deletion request not found' },
        { status: 404 }
      )
    }
    
    const deletionRequest = requestResult.rows[0]
    
    if (deletionRequest.status !== 'pending') {
      return NextResponse.json(
        { error: 'Request has already been processed' },
        { status: 400 }
      )
    }
    
    if (action === 'approve') {
      // Process the IP data deletion
      await processIPDataDeletion(deletionRequest.user_id, deletionRequest.email)
      
      // Update request status
      await query(`
        UPDATE data_deletion_requests 
        SET status = 'completed', processed_at = NOW(), processed_by = $1, notes = $2
        WHERE id = $3
      `, [adminUser.id, notes || 'IP data deletion completed', requestId])
      
      return NextResponse.json({
        success: true,
        message: 'IP data deletion completed successfully'
      })
      
    } else if (action === 'reject') {
      // Update request status to cancelled
      await query(`
        UPDATE data_deletion_requests 
        SET status = 'cancelled', processed_at = NOW(), processed_by = $1, notes = $2
        WHERE id = $3
      `, [adminUser.id, notes || 'Request rejected by admin', requestId])
      
      return NextResponse.json({
        success: true,
        message: 'IP data deletion request rejected'
      })
    }

  } catch (error) {
    console.error('Error processing IP deletion request:', error)
    return NextResponse.json(
      { error: 'Failed to process IP deletion request' },
      { status: 500 }
    )
  }
}

/**
 * Process IP data deletion for a user
 * This removes IP addresses from analytics data but keeps security logs
 */
async function processIPDataDeletion(userId: string, email: string): Promise<void> {
  try {
    // Remove IP addresses from search queries (analytics data)
    await query(`
      UPDATE search_queries 
      SET ip_address = NULL, user_agent = NULL
      WHERE user_id = $1
    `, [userId])
    
    // Remove IP addresses from company page views (analytics data)
    await query(`
      UPDATE company_page_views 
      SET ip_address = NULL, user_agent = NULL, referrer = NULL
      WHERE user_id = $1
    `, [userId])
    
    // Remove IP addresses from data export logs (compliance data)
    await query(`
      UPDATE data_export_logs 
      SET ip_address = NULL, user_agent = NULL
      WHERE user_id = $1
    `, [userId])
    
    // Note: We do NOT remove IP addresses from auth_logs as these are kept for security purposes
    // This is legally justified under legitimate business interests for fraud prevention
    
    // Log the deletion activity
    await query(`
      INSERT INTO activity_log (event_type, description, metadata)
      VALUES (
        'ip_data_deletion',
        'IP data deleted for user upon request',
        $1
      )
    `, [JSON.stringify({
      user_id: userId,
      email: email,
      deleted_from: ['search_queries', 'company_page_views', 'data_export_logs'],
      retained_in: ['auth_logs'],
      retention_reason: 'Security and fraud prevention (legitimate business interest)',
      processed_at: new Date().toISOString()
    })])
    
  } catch (error) {
    console.error('Error processing IP data deletion:', error)
    throw error
  }
}
