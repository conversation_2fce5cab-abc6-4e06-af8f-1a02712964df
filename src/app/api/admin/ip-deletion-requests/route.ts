import { NextRequest, NextResponse } from 'next/server'
import { requireAdmin } from '@/lib/auth'
import { query } from '@/lib/local-db'

// GET /api/admin/ip-deletion-requests - Get all IP deletion requests
export async function GET(request: NextRequest) {
  try {
    await requireAdmin()
    
    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get('limit') || '50')
    const status = searchParams.get('status') // optional filter
    
    let sql = `
      SELECT 
        id, user_id, email, reason, status, 
        created_at, processed_at, processed_by, notes
      FROM data_deletion_requests
    `
    const params: any[] = []
    
    if (status) {
      sql += ` WHERE status = $1`
      params.push(status)
    }
    
    sql += ` ORDER BY created_at DESC LIMIT $${params.length + 1}`
    params.push(limit)
    
    const result = await query(sql, params)
    
    return NextResponse.json({
      success: true,
      requests: result.rows,
      total: result.rows.length
    })

  } catch (error) {
    console.error('Error fetching IP deletion requests:', error)
    return NextResponse.json(
      { error: 'Failed to fetch IP deletion requests' },
      { status: 500 }
    )
  }
}

// POST /api/admin/ip-deletion-requests - Create new IP deletion request (for testing)
export async function POST(request: NextRequest) {
  try {
    await requireAdmin()
    
    const body = await request.json()
    const { user_id, email, reason } = body
    
    if (!user_id || !email || !reason) {
      return NextResponse.json(
        { error: 'user_id, email, and reason are required' },
        { status: 400 }
      )
    }
    
    const result = await query(`
      INSERT INTO data_deletion_requests (user_id, email, reason, status)
      VALUES ($1, $2, $3, 'pending')
      RETURNING id, created_at
    `, [user_id, email, reason])
    
    return NextResponse.json({
      success: true,
      request: result.rows[0]
    })

  } catch (error) {
    console.error('Error creating IP deletion request:', error)
    return NextResponse.json(
      { error: 'Failed to create IP deletion request' },
      { status: 500 }
    )
  }
}
