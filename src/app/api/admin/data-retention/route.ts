import { NextRequest, NextResponse } from 'next/server'
import { requireAdmin } from '@/lib/auth'
import { runDataRetentionCleanup, runIPAnonymization, getCleanupHistory } from '@/lib/data-retention-cleanup'

// GET /api/admin/data-retention - Get cleanup history
export async function GET(request: NextRequest) {
  try {
    await requireAdmin()
    
    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get('limit') || '50')
    
    const history = await getCleanupHistory(limit)
    
    return NextResponse.json({
      success: true,
      history,
      total: history.length
    })

  } catch (error) {
    console.error('Error fetching cleanup history:', error)
    return NextResponse.json(
      { error: 'Failed to fetch cleanup history' },
      { status: 500 }
    )
  }
}

// POST /api/admin/data-retention - Trigger manual cleanup
export async function POST(request: NextRequest) {
  try {
    await requireAdmin()
    
    const body = await request.json()
    const { type = 'comprehensive' } = body
    
    let result
    
    if (type === 'ip_only') {
      result = await runIPAnonymization()
    } else {
      result = await runDataRetentionCleanup()
    }
    
    return NextResponse.json({
      success: result.success,
      result,
      message: result.success 
        ? `Cleanup completed successfully. ${result.totalRecordsAffected} records affected.`
        : `Cleanup failed: ${result.error}`
    })

  } catch (error) {
    console.error('Error running data retention cleanup:', error)
    return NextResponse.json(
      { error: 'Failed to run data retention cleanup' },
      { status: 500 }
    )
  }
}
