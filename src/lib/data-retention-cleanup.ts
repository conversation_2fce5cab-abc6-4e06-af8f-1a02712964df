/**
 * Data Retention Cleanup Service
 * Implements GDPR-compliant data retention policies
 */

import { query } from './local-db'
import { logger } from './logger'

export interface CleanupResult {
  operation: string
  recordsAffected: number
  completedAt: string
}

export interface CleanupSummary {
  totalOperations: number
  totalRecordsAffected: number
  operations: CleanupResult[]
  startedAt: Date
  completedAt: Date
  success: boolean
  error?: string
}

/**
 * Run comprehensive data retention cleanup
 * This should be called periodically (e.g., daily via cron job)
 */
export async function runDataRetentionCleanup(): Promise<CleanupSummary> {
  const startedAt = new Date()
  let success = false
  let error: string | undefined

  logger.info('Starting data retention cleanup', {
    startedAt: startedAt.toISOString(),
    operation: 'data_retention_cleanup'
  })

  try {
    // Run the comprehensive cleanup function
    const result = await query(`
      SELECT * FROM comprehensive_data_retention_cleanup()
    `)

    const operations: CleanupResult[] = result.rows.map(row => ({
      operation: row.operation,
      recordsAffected: parseInt(row.records_affected) || 0,
      completedAt: row.completed_at
    }))

    const totalRecordsAffected = operations.reduce(
      (sum, op) => sum + op.recordsAffected, 
      0
    )

    success = true
    const completedAt = new Date()

    const summary: CleanupSummary = {
      totalOperations: operations.length,
      totalRecordsAffected,
      operations,
      startedAt,
      completedAt,
      success
    }

    // Log the cleanup results
    await query(`
      INSERT INTO data_retention_log (
        cleanup_type, records_affected, operation_details, 
        started_at, completed_at, status
      ) VALUES ($1, $2, $3, $4, $5, $6)
    `, [
      'comprehensive_cleanup',
      totalRecordsAffected,
      JSON.stringify(operations),
      startedAt,
      completedAt,
      'completed'
    ])

    logger.info('Data retention cleanup completed successfully', {
      totalOperations: operations.length,
      totalRecordsAffected,
      duration: completedAt.getTime() - startedAt.getTime(),
      operations: operations.map(op => `${op.operation}: ${op.recordsAffected}`)
    })

    return summary

  } catch (err) {
    error = err instanceof Error ? err.message : String(err)
    success = false
    const completedAt = new Date()

    // Log the failure
    try {
      await query(`
        INSERT INTO data_retention_log (
          cleanup_type, records_affected, operation_details, 
          started_at, completed_at, status, error_message
        ) VALUES ($1, $2, $3, $4, $5, $6, $7)
      `, [
        'comprehensive_cleanup',
        0,
        JSON.stringify({ error }),
        startedAt,
        completedAt,
        'failed',
        error
      ])
    } catch (logError) {
      logger.error('Failed to log cleanup failure', { error: logError })
    }

    logger.error('Data retention cleanup failed', {
      error,
      duration: completedAt.getTime() - startedAt.getTime()
    })

    return {
      totalOperations: 0,
      totalRecordsAffected: 0,
      operations: [],
      startedAt,
      completedAt,
      success,
      error
    }
  }
}

/**
 * Run only IP address anonymization (lighter operation)
 */
export async function runIPAnonymization(): Promise<CleanupSummary> {
  const startedAt = new Date()
  let success = false
  let error: string | undefined

  logger.info('Starting IP address anonymization', {
    startedAt: startedAt.toISOString(),
    operation: 'ip_anonymization'
  })

  try {
    const result = await query(`
      SELECT * FROM cleanup_old_analytics_data()
    `)

    const row = result.rows[0]
    const operations: CleanupResult[] = [
      {
        operation: 'search_queries_anonymized',
        recordsAffected: parseInt(row.search_queries_anonymized) || 0,
        completedAt: new Date().toISOString()
      },
      {
        operation: 'page_views_anonymized', 
        recordsAffected: parseInt(row.page_views_anonymized) || 0,
        completedAt: new Date().toISOString()
      },
      {
        operation: 'old_exports_deleted',
        recordsAffected: parseInt(row.old_exports_deleted) || 0,
        completedAt: new Date().toISOString()
      }
    ]

    const totalRecordsAffected = operations.reduce(
      (sum, op) => sum + op.recordsAffected, 
      0
    )

    success = true
    const completedAt = new Date()

    const summary: CleanupSummary = {
      totalOperations: operations.length,
      totalRecordsAffected,
      operations,
      startedAt,
      completedAt,
      success
    }

    // Log the cleanup results
    await query(`
      INSERT INTO data_retention_log (
        cleanup_type, records_affected, operation_details, 
        started_at, completed_at, status
      ) VALUES ($1, $2, $3, $4, $5, $6)
    `, [
      'ip_anonymization',
      totalRecordsAffected,
      JSON.stringify(operations),
      startedAt,
      completedAt,
      'completed'
    ])

    logger.info('IP anonymization completed successfully', {
      totalRecordsAffected,
      duration: completedAt.getTime() - startedAt.getTime()
    })

    return summary

  } catch (err) {
    error = err instanceof Error ? err.message : String(err)
    success = false
    const completedAt = new Date()

    logger.error('IP anonymization failed', { error })

    return {
      totalOperations: 0,
      totalRecordsAffected: 0,
      operations: [],
      startedAt,
      completedAt,
      success,
      error
    }
  }
}

/**
 * Get cleanup history for admin review
 */
export async function getCleanupHistory(limit: number = 50): Promise<any[]> {
  try {
    const result = await query(`
      SELECT 
        cleanup_type,
        records_affected,
        operation_details,
        started_at,
        completed_at,
        status,
        error_message
      FROM data_retention_log
      ORDER BY started_at DESC
      LIMIT $1
    `, [limit])

    return result.rows
  } catch (error) {
    logger.error('Failed to fetch cleanup history', { error })
    return []
  }
}
